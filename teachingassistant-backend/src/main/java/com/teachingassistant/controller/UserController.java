package com.teachingassistant.controller;

import com.teachingassistant.common.Result;
import com.teachingassistant.common.ResultCode;
import com.teachingassistant.dto.ChangePasswordRequest;
import com.teachingassistant.dto.SendEmailCodeRequest;
import com.teachingassistant.dto.UpdateEmailRequest;
import com.teachingassistant.dto.UpdateUserInfoRequest;
import com.teachingassistant.entity.User;
import com.teachingassistant.exception.BusinessException;
import com.teachingassistant.security.UserPrincipal;
import com.teachingassistant.service.EmailService;
import com.teachingassistant.service.UserService;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.security.core.Authentication;
import org.springframework.security.core.context.SecurityContextHolder;
import org.springframework.web.bind.annotation.*;

import jakarta.validation.Valid;

/**
 * 用户控制器
 * 
 * <AUTHOR> Assistant System
 */
@Slf4j
@RestController
@RequestMapping("/user")
@RequiredArgsConstructor
public class UserController {

    private final UserService userService;
    private final EmailService emailService;

    /**
     * 修改密码
     */
    @PutMapping("/password")
    public Result<Void> changePassword(@Valid @RequestBody ChangePasswordRequest request) {
        try {
            Authentication authentication = SecurityContextHolder.getContext().getAuthentication();
            if (authentication == null || !(authentication.getPrincipal() instanceof UserPrincipal)) {
                throw new BusinessException(ResultCode.UNAUTHORIZED);
            }
            
            UserPrincipal userPrincipal = (UserPrincipal) authentication.getPrincipal();
            Long userId = userPrincipal.getUserId();
            
            userService.updatePassword(userId, request.getOldPassword(), request.getNewPassword());
            
            log.info("用户修改密码成功: {}", userId);
            return Result.success("密码修改成功");
            
        } catch (BusinessException e) {
            throw e;
        } catch (Exception e) {
            log.error("修改密码失败", e);
            throw new BusinessException(ResultCode.SYSTEM_ERROR, "密码修改失败");
        }
    }
    
    /**
     * 发送邮箱验证码
     */
    @PostMapping("/email/send-code")
    public Result<Void> sendEmailVerificationCode(@Valid @RequestBody SendEmailCodeRequest request) {
        try {
            // 检查邮箱是否已被其他用户使用
            if (userService.existsByEmail(request.getEmail())) {
                throw new BusinessException(ResultCode.USER_EXISTS, "邮箱已被使用");
            }

            // 生成验证码
            String code = emailService.generateVerificationCode();

            // 存储验证码
            emailService.storeVerificationCode(request.getEmail(), code);

            // 发送验证码邮件
            emailService.sendVerificationCode(request.getEmail(), code);

            log.info("邮箱验证码发送成功: {}", request.getEmail());
            return Result.success("验证码已发送到您的邮箱");

        } catch (BusinessException e) {
            throw e;
        } catch (Exception e) {
            log.error("发送邮箱验证码失败", e);
            throw new BusinessException(ResultCode.SYSTEM_ERROR, "验证码发送失败");
        }
    }

    /**
     * 更换邮箱
     */
    @PutMapping("/email")
    public Result<Void> updateEmail(@Valid @RequestBody UpdateEmailRequest request) {
        try {
            Authentication authentication = SecurityContextHolder.getContext().getAuthentication();
            if (authentication == null || !(authentication.getPrincipal() instanceof UserPrincipal)) {
                throw new BusinessException(ResultCode.UNAUTHORIZED);
            }

            UserPrincipal userPrincipal = (UserPrincipal) authentication.getPrincipal();
            Long userId = userPrincipal.getUserId();

            // 验证邮箱验证码
            if (!emailService.verifyCode(request.getEmail(), request.getVerificationCode())) {
                throw new BusinessException(ResultCode.PARAM_INVALID, "验证码错误或已过期");
            }

            // 更新邮箱
            userService.updateEmail(userId, request.getEmail());

            // 删除已使用的验证码
            emailService.removeVerificationCode(request.getEmail());

            log.info("用户更换邮箱成功: {}", userId);
            return Result.success("邮箱更换成功");

        } catch (BusinessException e) {
            throw e;
        } catch (Exception e) {
            log.error("更换邮箱失败", e);
            throw new BusinessException(ResultCode.SYSTEM_ERROR, "邮箱更换失败");
        }
    }
    
    /**
     * 更新用户信息
     */
    @PutMapping("/info")
    public Result<Void> updateUserInfo(@Valid @RequestBody UpdateUserInfoRequest request) {
        try {
            Authentication authentication = SecurityContextHolder.getContext().getAuthentication();
            if (authentication == null || !(authentication.getPrincipal() instanceof UserPrincipal)) {
                throw new BusinessException(ResultCode.UNAUTHORIZED);
            }

            UserPrincipal userPrincipal = (UserPrincipal) authentication.getPrincipal();
            Long userId = userPrincipal.getUserId();

            User user = new User();
            user.setUserId(userId);
            user.setRealName(request.getRealName());

            userService.updateUser(user);

            log.info("用户信息更新成功: {}", userId);
            return Result.success("用户信息更新成功");

        } catch (BusinessException e) {
            throw e;
        } catch (Exception e) {
            log.error("更新用户信息失败", e);
            throw new BusinessException(ResultCode.SYSTEM_ERROR, "用户信息更新失败");
        }
    }
}
