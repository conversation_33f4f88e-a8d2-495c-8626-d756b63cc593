package com.teachingassistant.controller;

import com.teachingassistant.common.Result;
import com.teachingassistant.common.ResultCode;
import com.teachingassistant.config.JwtProperties;
import com.teachingassistant.dto.LoginRequest;
import com.teachingassistant.dto.LoginResponse;
import com.teachingassistant.entity.User;
import com.teachingassistant.exception.BusinessException;
import com.teachingassistant.security.UserPrincipal;
import com.teachingassistant.service.UserService;
import com.teachingassistant.util.JwtUtil;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.security.authentication.AuthenticationManager;
import org.springframework.security.authentication.UsernamePasswordAuthenticationToken;
import org.springframework.security.core.Authentication;
import org.springframework.security.core.context.SecurityContextHolder;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;

import jakarta.validation.Valid;

/**
 * 认证控制器
 * 
 * <AUTHOR> Assistant System
 */
@Slf4j
@RestController
@RequiredArgsConstructor
@Validated
public class AuthController {
    
    private final AuthenticationManager authenticationManager;
    private final JwtUtil jwtUtil;
    private final JwtProperties jwtProperties;
    private final UserService userService;
    
    /**
     * 用户登录
     */
    @PostMapping("/login")
    public Result<LoginResponse> login(@Valid @RequestBody LoginRequest request) {
        try {
            log.info("用户登录请求: {}", request.getUsername());
            
            // 执行认证
            Authentication authentication = authenticationManager.authenticate(
                new UsernamePasswordAuthenticationToken(request.getUsername(), request.getPassword())
            );
            
            // 设置安全上下文
            SecurityContextHolder.getContext().setAuthentication(authentication);
            
            // 获取用户信息
            UserPrincipal userPrincipal = (UserPrincipal) authentication.getPrincipal();
            User user = userPrincipal.getUser();

            // 更新最后登录时间
            userService.updateLastLoginTime(user.getUserId());

            // 生成JWT Token
            String token = jwtUtil.generateToken(
                user.getUsername(),
                user.getRole(),
                user.getUserId(),
                user.getSchoolId()
            );
            
            // 构建响应
            LoginResponse.UserInfo userInfo = new LoginResponse.UserInfo();
            userInfo.setUserId(user.getUserId());
            userInfo.setUsername(user.getUsername());
            userInfo.setRealName(user.getRealName());
            userInfo.setRole(user.getRole());
            userInfo.setSchoolId(user.getSchoolId());
            userInfo.setSchoolName(user.getSchool() != null ? user.getSchool().getName() : null);
            userInfo.setPhone(user.getPhone());
            userInfo.setEmail(user.getEmail());
            userInfo.setGender(user.getGender());
            userInfo.setHireDate(user.getHireDate());
            userInfo.setSubject(user.getSubject());
            userInfo.setExperience(user.getExperience());
            userInfo.setBio(user.getBio());
            userInfo.setLastLoginAt(user.getLastLoginAt());
            userInfo.setStatus("active".equals(user.getStatus()) ? 1 : 0);
            
            LoginResponse response = new LoginResponse(
                token,
                jwtProperties.getExpiration() / 1000, // 转换为秒
                userInfo
            );
            
            log.info("用户登录成功: {}, 角色: {}", user.getUsername(), user.getRole());
            return Result.success("登录成功", response);
            
        } catch (Exception e) {
            log.warn("用户登录失败: {}, 错误: {}", request.getUsername(), e.getMessage());
            throw new BusinessException(ResultCode.LOGIN_FAILED, "用户名或密码错误");
        }
    }
    
    /**
     * 用户登出
     */
    @PostMapping("/logout")
    public Result<Void> logout() {
        try {
            Authentication authentication = SecurityContextHolder.getContext().getAuthentication();
            if (authentication != null && authentication.getPrincipal() instanceof UserPrincipal) {
                UserPrincipal userPrincipal = (UserPrincipal) authentication.getPrincipal();
                log.info("用户登出: {}", userPrincipal.getUsername());
            }
            
            // 清除安全上下文
            SecurityContextHolder.clearContext();
            
            return Result.success("登出成功");
        } catch (Exception e) {
            log.error("用户登出失败", e);
            return Result.error("登出失败");
        }
    }
    
    /**
     * 获取当前用户信息
     */
    @GetMapping("/user/profile")
    public Result<LoginResponse.UserInfo> getCurrentUser() {
        try {
            Authentication authentication = SecurityContextHolder.getContext().getAuthentication();
            if (authentication == null || !(authentication.getPrincipal() instanceof UserPrincipal)) {
                throw new BusinessException(ResultCode.UNAUTHORIZED);
            }
            
            UserPrincipal userPrincipal = (UserPrincipal) authentication.getPrincipal();
            User user = userService.findById(userPrincipal.getUserId());
            
            LoginResponse.UserInfo userInfo = new LoginResponse.UserInfo();
            userInfo.setUserId(user.getUserId());
            userInfo.setUsername(user.getUsername());
            userInfo.setRealName(user.getRealName());
            userInfo.setRole(user.getRole());
            userInfo.setSchoolId(user.getSchoolId());
            userInfo.setSchoolName(user.getSchool() != null ? user.getSchool().getName() : null);
            userInfo.setPhone(user.getPhone());
            userInfo.setEmail(user.getEmail());
            userInfo.setGender(user.getGender());
            userInfo.setHireDate(user.getHireDate());
            userInfo.setSubject(user.getSubject());
            userInfo.setExperience(user.getExperience());
            userInfo.setBio(user.getBio());
            userInfo.setLastLoginAt(user.getLastLoginAt());
            userInfo.setStatus("active".equals(user.getStatus()) ? 1 : 0);
            
            return Result.success(userInfo);
        } catch (BusinessException e) {
            throw e;
        } catch (Exception e) {
            log.error("获取用户信息失败", e);
            throw new BusinessException(ResultCode.SYSTEM_ERROR);
        }
    }
    
    /**
     * 刷新Token
     */
    @PostMapping("/refresh-token")
    public Result<LoginResponse> refreshToken(@RequestHeader("Authorization") String authHeader) {
        try {
            if (authHeader == null || !authHeader.startsWith("Bearer ")) {
                throw new BusinessException(ResultCode.TOKEN_INVALID);
            }
            
            String token = authHeader.substring(7);
            String username = jwtUtil.getUsernameFromToken(token);
            
            if (username != null) {
                User user = userService.findByUsername(username);
                if (user != null && "active".equals(user.getStatus())) {
                    String newToken = jwtUtil.refreshToken(token);
                    
                    LoginResponse.UserInfo userInfo = new LoginResponse.UserInfo();
                    userInfo.setUserId(user.getUserId());
                    userInfo.setUsername(user.getUsername());
                    userInfo.setRealName(user.getRealName());
                    userInfo.setRole(user.getRole());
                    userInfo.setSchoolId(user.getSchoolId());
                    userInfo.setSchoolName(user.getSchool() != null ? user.getSchool().getName() : null);
                    userInfo.setPhone(user.getPhone());
                    userInfo.setEmail(user.getEmail());
                    userInfo.setGender(user.getGender());
                    userInfo.setHireDate(user.getHireDate());
                    userInfo.setSubject(user.getSubject());
                    userInfo.setExperience(user.getExperience());
                    userInfo.setBio(user.getBio());
                    userInfo.setLastLoginAt(user.getLastLoginAt());
                    userInfo.setStatus("active".equals(user.getStatus()) ? 1 : 0);
                    
                    LoginResponse response = new LoginResponse(
                        newToken,
                        jwtProperties.getExpiration() / 1000,
                        userInfo
                    );
                    
                    return Result.success("Token刷新成功", response);
                }
            }
            
            throw new BusinessException(ResultCode.TOKEN_INVALID);
        } catch (BusinessException e) {
            throw e;
        } catch (Exception e) {
            log.error("Token刷新失败", e);
            throw new BusinessException(ResultCode.TOKEN_INVALID);
        }
    }
}
