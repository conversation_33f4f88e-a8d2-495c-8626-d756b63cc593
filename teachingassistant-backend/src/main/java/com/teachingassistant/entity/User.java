package com.teachingassistant.entity;

import com.fasterxml.jackson.annotation.JsonIgnore;
import lombok.Data;
import lombok.EqualsAndHashCode;

/**
 * 用户实体类
 * 
 * <AUTHOR> Assistant System
 */
@Data
@EqualsAndHashCode(callSuper = true)
public class User extends BaseEntity {
    
    /**
     * 用户ID
     */
    private Long userId;
    
    /**
     * 所属学校ID
     */
    private Long schoolId;
    
    /**
     * 登录账号
     */
    private String username;
    
    /**
     * 加密密码
     */
    @JsonIgnore
    private String password;
    
    /**
     * 用户角色：super_admin-超级管理员，principal-校长，teacher-老师
     */
    private String role;
    
    /**
     * 真实姓名
     */
    private String realName;
    
    /**
     * 绑定手机
     */
    private String phone;

    /**
     * 邮箱地址
     */
    private String email;

    /**
     * MFA设备密钥
     */
    @JsonIgnore
    private String mfaSecret;
    
    /**
     * 用户状态：active-启用，inactive-停用
     */
    private String status;
    
    /**
     * 学校信息（关联查询时使用）
     */
    private School school;
}
