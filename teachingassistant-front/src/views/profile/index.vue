<template>
  <div class="profile-container">
    <div class="header">
      <h2>个人信息</h2>
      <el-button type="primary" @click="handleEdit">
        <el-icon><Edit /></el-icon>
        编辑信息
      </el-button>
    </div>

    <div class="profile-content">
      <el-row :gutter="20">
        <el-col :span="8">
          <el-card class="avatar-card">
            <div class="avatar-section">
              <el-avatar :size="120" :src="getDefaultAvatar(userInfo?.role)" class="user-avatar">
                <el-icon><User /></el-icon>
              </el-avatar>
              <div class="avatar-info">
                <h3>{{ userInfo?.realName || '未设置' }}</h3>
                <p>{{ getRoleText(userInfo?.role) }}</p>
              </div>
            </div>
            
            <div class="quick-stats">
              <div class="stat-item">
                <div class="stat-value">{{ userStats.workDays }}</div>
                <div class="stat-label">工作天数</div>
              </div>
              <div class="stat-item">
                <div class="stat-value">{{ userStats.totalClasses }}</div>
                <div class="stat-label">授课总数</div>
              </div>
              <div class="stat-item">
                <div class="stat-value">{{ userStats.totalStudents }}</div>
                <div class="stat-label">学生总数</div>
              </div>
            </div>
          </el-card>
        </el-col>

        <el-col :span="16">
          <el-card class="info-card">
            <template #header>
              <div class="card-header">
                <span>基本信息</span>
              </div>
            </template>
            
            <el-descriptions :column="2" border>
              <el-descriptions-item label="用户名">{{ userInfo?.username || '未设置' }}</el-descriptions-item>
              <el-descriptions-item label="真实姓名">{{ userInfo?.realName || '未设置' }}</el-descriptions-item>
              <el-descriptions-item label="角色">{{ getRoleText(userInfo?.role) }}</el-descriptions-item>
              <el-descriptions-item label="性别">{{ userInfo?.gender === 'M' ? '男' : (userInfo?.gender === 'F' ? '女' : '未设置') }}</el-descriptions-item>
              <el-descriptions-item label="手机号">{{ userInfo?.phone || '未设置' }}</el-descriptions-item>
              <el-descriptions-item label="邮箱">{{ userInfo?.email || '未设置' }}</el-descriptions-item>
              <el-descriptions-item label="入职时间">{{ formatDate(userInfo?.hireDate) || '未设置' }}</el-descriptions-item>
              <el-descriptions-item label="所属学校">{{ userInfo?.schoolName || '未设置' }}</el-descriptions-item>
              <el-descriptions-item label="主教科目" v-if="userInfo?.role === 'teacher'">{{ userInfo?.subject || '未设置' }}</el-descriptions-item>
              <el-descriptions-item label="教学经验" v-if="userInfo?.role === 'teacher'">{{ userInfo?.experience || 0 }}年</el-descriptions-item>
              <el-descriptions-item label="最后登录">{{ formatDate(userInfo?.lastLoginAt) || '未知' }}</el-descriptions-item>
              <el-descriptions-item label="账号状态">
                <el-tag :type="userInfo?.status === 1 ? 'success' : 'danger'">
                  {{ userInfo?.status === 1 ? '正常' : '禁用' }}
                </el-tag>
              </el-descriptions-item>
            </el-descriptions>
          </el-card>

          <el-card class="security-card" style="margin-top: 20px;">
            <template #header>
              <div class="card-header">
                <span>安全设置</span>
              </div>
            </template>
            
            <div class="security-items">
              <div class="security-item">
                <div class="security-info">
                  <div class="security-title">登录密码</div>
                  <div class="security-desc">用于登录系统的密码</div>
                </div>
                <el-button type="primary" @click="handleChangePassword">修改密码</el-button>
              </div>
              
              <div class="security-item">
                <div class="security-info">
                  <div class="security-title">邮箱地址</div>
                  <div class="security-desc">已绑定邮箱：{{ userInfo?.email || '未设置' }}</div>
                </div>
                <el-button type="primary" @click="handleChangeEmail">更换邮箱</el-button>
              </div>
              
            </div>
          </el-card>
        </el-col>
      </el-row>
    </div>

    <!-- 编辑信息对话框 -->
    <el-dialog
      v-model="editDialogVisible"
      title="编辑个人信息"
      width="600px"
      @close="handleEditDialogClose"
    >
      <el-form
        ref="editFormRef"
        :model="editForm"
        :rules="editRules"
        label-width="100px"
      >
        <el-form-item label="真实姓名" prop="realName">
          <el-input v-model="editForm.realName" placeholder="请输入真实姓名" />
        </el-form-item>
        <el-form-item label="性别" prop="gender">
          <el-radio-group v-model="editForm.gender">
            <el-radio label="M">男</el-radio>
            <el-radio label="F">女</el-radio>
          </el-radio-group>
        </el-form-item>
        <el-form-item label="手机号" prop="phone">
          <el-input v-model="editForm.phone" placeholder="请输入手机号" readonly />
        </el-form-item>
        <el-form-item label="邮箱" prop="email">
          <el-input v-model="editForm.email" placeholder="请输入邮箱" readonly />
        </el-form-item>
        <el-form-item label="入职时间" prop="hireDate">
          <el-date-picker
            v-model="editForm.hireDate"
            type="date"
            placeholder="请选择入职时间"
            format="YYYY-MM-DD"
            value-format="YYYY-MM-DD"
            style="width: 100%"
          />
        </el-form-item>
        <el-form-item label="主教科目" prop="subject" v-if="userInfo?.role === 'teacher'">
          <el-input v-model="editForm.subject" placeholder="请输入主教科目" />
        </el-form-item>
        <el-form-item label="教学经验" prop="experience" v-if="userInfo?.role === 'teacher'">
          <el-input-number
            v-model="editForm.experience"
            :min="0"
            :max="50"
            placeholder="请输入教学经验年数"
            style="width: 100%"
          />
        </el-form-item>
        <el-form-item label="个人简介" prop="bio">
          <el-input
            v-model="editForm.bio"
            type="textarea"
            :rows="4"
            placeholder="请输入个人简介"
          />
        </el-form-item>
      </el-form>
      <template #footer>
        <span class="dialog-footer">
          <el-button @click="editDialogVisible = false">取消</el-button>
          <el-button type="primary" @click="handleSaveEdit">保存</el-button>
        </span>
      </template>
    </el-dialog>

    <!-- 修改密码对话框 -->
    <el-dialog
      v-model="passwordDialogVisible"
      title="修改密码"
      width="500px"
      @close="handlePasswordDialogClose"
    >
      <el-form
        ref="passwordFormRef"
        :model="passwordForm"
        :rules="passwordRules"
        label-width="100px"
      >
        <el-form-item label="当前密码" prop="oldPassword">
          <el-input
            v-model="passwordForm.oldPassword"
            type="password"
            placeholder="请输入当前密码"
            show-password
          />
        </el-form-item>
        <el-form-item label="新密码" prop="newPassword">
          <el-input
            v-model="passwordForm.newPassword"
            type="password"
            placeholder="请输入新密码"
            show-password
          />
        </el-form-item>
        <el-form-item label="确认密码" prop="confirmPassword">
          <el-input
            v-model="passwordForm.confirmPassword"
            type="password"
            placeholder="请再次输入新密码"
            show-password
          />
        </el-form-item>
      </el-form>
      <template #footer>
        <span class="dialog-footer">
          <el-button @click="passwordDialogVisible = false">取消</el-button>
          <el-button type="primary" @click="handleSavePassword">确定</el-button>
        </span>
      </template>
    </el-dialog>

    <!-- 更换邮箱对话框 -->
    <el-dialog
      v-model="emailDialogVisible"
      title="更换邮箱"
      width="450px"
      @close="handleEmailDialogClose"
    >
      <el-form
        ref="emailFormRef"
        :model="emailForm"
        :rules="emailRules"
        label-width="100px"
      >
        <el-form-item label="新邮箱" prop="email">
          <el-input
            v-model="emailForm.email"
            placeholder="请输入新邮箱地址"
          />
        </el-form-item>
        <el-form-item label="验证码" prop="verificationCode">
          <div class="verification-code-input">
            <el-input
              v-model="emailForm.verificationCode"
              placeholder="请输入验证码"
              style="flex: 1; margin-right: 10px;"
            />
            <el-button
              type="primary"
              :disabled="!emailForm.email || sendCodeLoading || countdown > 0"
              :loading="sendCodeLoading"
              @click="handleSendEmailCode"
            >
              {{ countdown > 0 ? `${countdown}s后重发` : '发送验证码' }}
            </el-button>
          </div>
        </el-form-item>
      </el-form>

      <template #footer>
        <span class="dialog-footer">
          <el-button @click="emailDialogVisible = false">取消</el-button>
          <el-button type="primary" @click="handleSaveEmail">确定</el-button>
        </span>
      </template>
    </el-dialog>




  </div>
</template>

<script setup>
import { ref, reactive, onMounted, computed } from 'vue'
import { ElMessage } from 'element-plus'
import { Edit, User } from '@element-plus/icons-vue'
import { useUserStore } from '@/stores/user'

// Store
const userStore = useUserStore()

// 响应式数据
const editDialogVisible = ref(false)
const passwordDialogVisible = ref(false)
const emailDialogVisible = ref(false)
const editFormRef = ref()
const passwordFormRef = ref()
const emailFormRef = ref()
const sendCodeLoading = ref(false)
const countdown = ref(0)

// 用户信息 - 从 store 获取
const userInfo = computed(() => userStore.userInfo || {
  userId: 0,
  username: '',
  realName: '',
  role: '',
  gender: '',
  phone: '',
  email: '',
  avatar: '',
  hireDate: '',
  schoolName: '',
  subject: '',
  experience: 0,
  lastLoginAt: '',
  status: 1,
  bio: ''
})

// 用户统计
const userStats = ref({
  workDays: 120,
  totalClasses: 240,
  totalStudents: 150
})

// 编辑表单
const editForm = reactive({
  realName: '',
  gender: '',
  phone: '',
  email: '',
  hireDate: '',
  subject: '',
  experience: 0,
  bio: ''
})

// 密码表单
const passwordForm = reactive({
  oldPassword: '',
  newPassword: '',
  confirmPassword: ''
})

// 邮箱表单
const emailForm = reactive({
  email: '',
  verificationCode: ''
})

// 表单验证规则
const editRules = {
  realName: [
    { required: true, message: '请输入真实姓名', trigger: 'blur' }
  ],
  hireDate: [
    { required: true, message: '请选择入职时间', trigger: 'change' }
  ],
  subject: [
    { required: true, message: '请输入主教科目', trigger: 'blur' }
  ],
  experience: [
    { required: true, message: '请输入教学经验', trigger: 'blur' },
    { type: 'number', min: 0, max: 50, message: '教学经验应在0-50年之间', trigger: 'blur' }
  ]
}

const passwordRules = {
  oldPassword: [
    { required: true, message: '请输入当前密码', trigger: 'blur' }
  ],
  newPassword: [
    { required: true, message: '请输入新密码', trigger: 'blur' },
    { min: 6, max: 20, message: '密码长度在 6 到 20 个字符', trigger: 'blur' }
  ],
  confirmPassword: [
    { required: true, message: '请再次输入新密码', trigger: 'blur' },
    {
      validator: (rule, value, callback) => {
        if (value !== passwordForm.newPassword) {
          callback(new Error('两次输入密码不一致'))
        } else {
          callback()
        }
      },
      trigger: 'blur'
    }
  ]
}

// 邮箱验证规则
const emailRules = {
  email: [
    { required: true, message: '请输入邮箱地址', trigger: 'blur' },
    { type: 'email', message: '请输入正确的邮箱格式', trigger: 'blur' }
  ],
  verificationCode: [
    { required: true, message: '请输入验证码', trigger: 'blur' },
    { pattern: /^\d{6}$/, message: '验证码为6位数字', trigger: 'blur' }
  ]
}

// 方法
const getRoleText = (role) => {
  const roleMap = {
    super_admin: '超级管理员',
    principal: '校长',
    teacher: '教师',
    student: '学生'
  }
  return roleMap[role] || role
}

const getDefaultAvatar = (role) => {
  // 使用import方式引入SVG文件
  const avatarMap = {
    super_admin: new URL('/src/assets/avatars/admin-avatar.svg', import.meta.url).href,
    principal: new URL('/src/assets/avatars/principal-avatar.svg', import.meta.url).href,
    teacher: new URL('/src/assets/avatars/teacher-avatar.svg', import.meta.url).href
  }
  return avatarMap[role] || new URL('/src/assets/avatars/default-avatar.svg', import.meta.url).href
}

const formatDate = (dateString) => {
  if (!dateString) return ''
  const date = new Date(dateString)
  return date.toLocaleDateString('zh-CN')
}

const fetchUserInfo = async () => {
  try {
    // 如果 store 中没有用户信息，则获取
    if (!userStore.userInfo) {
      await userStore.getUserInfo()
    }
  } catch (error) {
    ElMessage.error('获取用户信息失败')
  }
}

const fetchUserStats = async () => {
  try {
    // TODO: 调用API获取用户统计数据
    // 模拟数据已在上面定义
  } catch (error) {
    ElMessage.error('获取统计数据失败')
  }
}

const handleEdit = () => {
  const user = userInfo.value
  Object.assign(editForm, {
    realName: user.realName || '',
    gender: user.gender || '',
    phone: user.phone || '',
    email: user.email || '',
    hireDate: user.hireDate || '',
    subject: user.subject || '',
    experience: user.experience || 0,
    bio: user.bio || ''
  })
  editDialogVisible.value = true
}

const handleSaveEdit = async () => {
  try {
    await editFormRef.value.validate()

    // 构建更新数据
    const updateData = {
      realName: editForm.realName,
      gender: editForm.gender,
      hireDate: editForm.hireDate,
      bio: editForm.bio
    }

    // 如果是教师角色，添加教师特有字段
    if (userInfo.value?.role === 'teacher') {
      updateData.subject = editForm.subject
      updateData.experience = editForm.experience
    }

    // 调用API更新用户信息
    await userStore.updateProfile(updateData)

    ElMessage.success('信息更新成功')
    editDialogVisible.value = false
  } catch (error) {
    ElMessage.error('保存失败')
  }
}

const handleEditDialogClose = () => {
  editFormRef.value?.resetFields()
}

const handleChangePassword = () => {
  passwordForm.oldPassword = ''
  passwordForm.newPassword = ''
  passwordForm.confirmPassword = ''
  passwordDialogVisible.value = true
}

const handleSavePassword = async () => {
  try {
    await passwordFormRef.value.validate()

    // 调用API修改密码
    await userStore.changePassword(
      passwordForm.oldPassword,
      passwordForm.newPassword,
      passwordForm.confirmPassword
    )

    ElMessage.success('密码修改成功')
    passwordDialogVisible.value = false
  } catch (error) {
    ElMessage.error('密码修改失败')
  }
}

const handlePasswordDialogClose = () => {
  passwordFormRef.value?.resetFields()
}

const handleChangeEmail = () => {
  emailForm.email = ''
  emailForm.verificationCode = ''
  countdown.value = 0
  emailDialogVisible.value = true
}

const handleSendEmailCode = async () => {
  try {
    // 验证邮箱格式
    if (!emailForm.email) {
      ElMessage.error('请先输入邮箱地址')
      return
    }

    const emailRegex = /^[^\s@]+@[^\s@]+\.[^\s@]+$/
    if (!emailRegex.test(emailForm.email)) {
      ElMessage.error('请输入正确的邮箱格式')
      return
    }

    sendCodeLoading.value = true

    // 调用API发送验证码
    await userStore.sendEmailVerificationCode(emailForm.email)

    ElMessage.success('验证码已发送到您的邮箱')

    // 开始倒计时
    countdown.value = 60
    const timer = setInterval(() => {
      countdown.value--
      if (countdown.value <= 0) {
        clearInterval(timer)
      }
    }, 1000)

  } catch (error) {
    ElMessage.error('验证码发送失败')
  } finally {
    sendCodeLoading.value = false
  }
}

const handleSaveEmail = async () => {
  try {
    await emailFormRef.value.validate()

    // 调用API更换邮箱
    await userStore.updateEmail(emailForm.email, emailForm.verificationCode)

    ElMessage.success('邮箱更换成功')
    emailDialogVisible.value = false
  } catch (error) {
    ElMessage.error('邮箱更换失败')
  }
}

const handleEmailDialogClose = () => {
  emailFormRef.value?.resetFields()
  countdown.value = 0
}



// 生命周期
onMounted(() => {
  fetchUserInfo()
  fetchUserStats()
})
</script>

<style scoped>
.profile-container {
  padding: 20px;
}

.header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 20px;
}

.header h2 {
  margin: 0;
  color: #303133;
}

.profile-content {
  margin-top: 20px;
}

.avatar-card {
  text-align: center;
}

.avatar-section {
  padding: 20px 0;
  border-bottom: 1px solid #ebeef5;
  margin-bottom: 20px;
}

.user-avatar {
  margin-bottom: 15px;
}

.avatar-info h3 {
  margin: 0 0 5px 0;
  color: #303133;
}

.avatar-info p {
  margin: 0 0 10px 0;
  color: #909399;
}

.quick-stats {
  display: flex;
  justify-content: space-around;
}

.stat-item {
  text-align: center;
}

.stat-value {
  font-size: 24px;
  font-weight: bold;
  color: #409eff;
  margin-bottom: 5px;
}

.stat-label {
  font-size: 14px;
  color: #909399;
}

.info-card,
.security-card {
  height: fit-content;
}

.card-header {
  font-weight: bold;
  color: #303133;
}

.security-items {
  space-y: 20px;
}

.security-item {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 15px 0;
  border-bottom: 1px solid #ebeef5;
}

.security-item:last-child {
  border-bottom: none;
}

.security-info {
  flex: 1;
}

.security-title {
  font-weight: bold;
  color: #303133;
  margin-bottom: 5px;
}

.security-desc {
  color: #909399;
  font-size: 14px;
}



.dialog-footer {
  display: flex;
  justify-content: flex-end;
  gap: 10px;
}

.verification-code-input {
  display: flex;
  align-items: center;
}
</style>
