import { defineStore } from 'pinia'
import { ref } from 'vue'

export const useAppStore = defineStore('app', () => {
  // 侧边栏状态
  const sidebarCollapsed = ref(false)
  
  // 设备类型
  const device = ref<'desktop' | 'mobile'>('desktop')
  
  // 主题
  const theme = ref<'light' | 'dark'>('light')
  
  // 语言
  const language = ref('zh-CN')
  
  // 页面加载状态
  const pageLoading = ref(false)
  
  // 全局加载状态
  const globalLoading = ref(false)
  
  // 面包屑
  const breadcrumbs = ref<Array<{ title: string; path?: string }>>([])
  
  // 切换侧边栏
  const toggleSidebar = () => {
    sidebarCollapsed.value = !sidebarCollapsed.value
  }
  
  // 设置侧边栏状态
  const setSidebarCollapsed = (collapsed: boolean) => {
    sidebarCollapsed.value = collapsed
  }
  
  // 设置设备类型
  const setDevice = (deviceType: 'desktop' | 'mobile') => {
    device.value = deviceType
    
    // 移动端自动收起侧边栏
    if (deviceType === 'mobile') {
      sidebarCollapsed.value = true
    }
  }
  
  // 设置主题
  const setTheme = (newTheme: 'light' | 'dark') => {
    theme.value = newTheme
    document.documentElement.setAttribute('data-theme', newTheme)
    localStorage.setItem('teaching_assistant_theme', newTheme)
  }
  
  // 设置语言
  const setLanguage = (lang: string) => {
    language.value = lang
    localStorage.setItem('teaching_assistant_language', lang)
  }
  
  // 设置页面加载状态
  const setPageLoading = (loading: boolean) => {
    pageLoading.value = loading
  }
  
  // 设置全局加载状态
  const setGlobalLoading = (loading: boolean) => {
    globalLoading.value = loading
  }
  
  // 设置面包屑
  const setBreadcrumbs = (crumbs: Array<{ title: string; path?: string }>) => {
    breadcrumbs.value = crumbs
  }
  
  // 添加面包屑
  const addBreadcrumb = (crumb: { title: string; path?: string }) => {
    breadcrumbs.value.push(crumb)
  }
  
  // 清除面包屑
  const clearBreadcrumbs = () => {
    breadcrumbs.value = []
  }
  
  // 初始化应用设置
  const initAppSettings = () => {
    // 恢复主题设置
    const savedTheme = localStorage.getItem('teaching_assistant_theme') as 'light' | 'dark'
    if (savedTheme) {
      setTheme(savedTheme)
    }
    
    // 恢复语言设置
    const savedLanguage = localStorage.getItem('teaching_assistant_language')
    if (savedLanguage) {
      setLanguage(savedLanguage)
    }
    
    // 检测设备类型
    const checkDevice = () => {
      const width = window.innerWidth
      setDevice(width < 768 ? 'mobile' : 'desktop')
    }
    
    checkDevice()
    window.addEventListener('resize', checkDevice)
  }
  
  // 重置应用状态
  const resetAppState = () => {
    sidebarCollapsed.value = false
    pageLoading.value = false
    globalLoading.value = false
    breadcrumbs.value = []
  }
  
  return {
    // 状态
    sidebarCollapsed: readonly(sidebarCollapsed),
    device: readonly(device),
    theme: readonly(theme),
    language: readonly(language),
    pageLoading: readonly(pageLoading),
    globalLoading: readonly(globalLoading),
    breadcrumbs: readonly(breadcrumbs),
    
    // 方法
    toggleSidebar,
    setSidebarCollapsed,
    setDevice,
    setTheme,
    setLanguage,
    setPageLoading,
    setGlobalLoading,
    setBreadcrumbs,
    addBreadcrumb,
    clearBreadcrumbs,
    initAppSettings,
    resetAppState
  }
})

export default useAppStore
